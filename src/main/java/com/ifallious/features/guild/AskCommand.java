package com.ifallious.features.guild;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.GuildChatMessageEvent;
import gg.essential.universal.UChat;
import meteordevelopment.orbit.EventHandler;

public class AskCommand {
    public AskCommand() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onGuildChatMessage(GuildChatMessageEvent event) {
        UChat.chat("Event received: " + event.getMessage() + "by " + event.getUsername());
    }
}
