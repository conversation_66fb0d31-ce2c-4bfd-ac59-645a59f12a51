package com.ifallious.features.spells;

import com.ifallious.event.GlobalEventBus;
import com.ifallious.event.TickEvent;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.featureutils.SpellmacroUtils;
import com.wynntils.core.components.Models;
import com.wynntils.handlers.bossbar.TrackedBar;
import com.wynntils.handlers.bossbar.type.BossBarProgress;
import com.wynntils.utils.type.CappedValue;
import meteordevelopment.orbit.EventHandler;

import java.util.Arrays;
import java.util.Objects;

public class AutoTransfer {
    private long lastTransfer = 0;
    public AutoTransfer() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    public void onTick(TickEvent e) {
        if (!ConfigManager.getFeature("autoTransfer")) return;
        TrackedBar manaBankBar = Models.Ability.manaBankBar;
        if (manaBankBar == null) return;
        BossBarProgress barProgress = manaBankBar.getBarProgress();
        if (barProgress == null) return;
        CappedValue value = barProgress.value();
        double threshold = ConfigManager.getSetting("autoTransferThreshold");
        if (value.current() >= threshold && System.currentTimeMillis() - lastTransfer > 1000) {
            SpellmacroUtils.add(Arrays.asList("R","L","R"));
            lastTransfer = System.currentTimeMillis();
        }
    }
}
