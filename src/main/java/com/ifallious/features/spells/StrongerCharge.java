package com.ifallious.features.spells;
import com.ifallious.event.GlobalEventBus;
import com.ifallious.mixin.EntityVelocityUpdateS2CPacketAccessor;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.ErrorReporter;
import com.wynntils.core.components.Models;
import gg.essential.universal.UChat;
import meteordevelopment.orbit.EventHandler;
import net.minecraft.network.packet.s2c.play.EntityVelocityUpdateS2CPacket;
import com.ifallious.event.PacketEvent;
import org.apache.commons.lang3.exception.ExceptionUtils;

import static com.ifallious.Wynnutils.mc;

public class StrongerCharge {

    public StrongerCharge() {
        GlobalEventBus.subscribe(this);
    }

    @EventHandler
    private void onPacketReceive(PacketEvent.PacketReceiveEvent event) {
        if (!ConfigManager.getFeature("strongerCharge")) return;
        try {
            if (mc.player == null) return;
            if (event.packet instanceof EntityVelocityUpdateS2CPacket packet && packet.getEntityId() == mc.player.getId() && hasCharge()) {
                Double multiplier = ConfigManager.getSetting("chargeVelocityMultiplier");
                if (multiplier == null) multiplier = 1.2;
                double x = (packet.getVelocityX() * multiplier);
                double y = (packet.getVelocityY() * multiplier);
                double z = (packet.getVelocityZ() * multiplier);
                ((EntityVelocityUpdateS2CPacketAccessor) packet).setX((int) (x * 8000));
                ((EntityVelocityUpdateS2CPacketAccessor) packet).setY((int) (y * 8000));
                ((EntityVelocityUpdateS2CPacketAccessor) packet).setZ((int) (z * 8000));
            }
        } catch (Exception e) {
            ErrorReporter.reportError("StrongerCharge packet event failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }
    private static boolean hasCharge() {
        try {
            String Spell = Models.Spell.getLastSpellName();
            if (Spell == null) return false;
            if (Spell.equals("Charge")) return true;
            else return false;
        } catch (Exception e) {
            ErrorReporter.reportError("StrongerCharge hasCharge check failed", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }
}
