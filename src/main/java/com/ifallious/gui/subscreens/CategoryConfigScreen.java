package com.ifallious.gui.subscreens;

import com.ifallious.gui.GUIUtils;
import com.ifallious.gui.components.ConfigOption;
import com.ifallious.gui.components.MultiLineTextComponent;
import com.ifallious.gui.components.SliderComponent;
import com.ifallious.gui.components.ToggleSwitchComponent;
import com.ifallious.utils.config.ConfigManager;
import com.ifallious.utils.config.FeatureConfig;
import gg.essential.elementa.UIComponent;
import gg.essential.elementa.components.*;
import gg.essential.elementa.constraints.*;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

import static com.ifallious.gui.GUIUtils.appear;

/**
 * Abstract base class for category-specific configuration subscreens.
 * Automatically generates UI elements based on the provided options.
 */
public abstract class CategoryConfigScreen extends UIContainer {
    private final UIComponent parent;
    private final String title;
    private final List<ConfigOption> options = new ArrayList<>();
    private UIComponent scrollComponent;
    private Boolean first = false;
    private float scalingFactor = GUIUtils.getScale();

    /**
     * Creates a new CategoryConfigScreen with the given parent component and title.
     *
     * @param parent The parent component to attach to
     * @param title The title of the category
     */
    public CategoryConfigScreen(UIComponent parent, String title, Boolean first) {
        super();
        this.parent = parent;
        this.title = title;
        this.first = first;
        this.setX(new CenterConstraint())
            .setY(new CenterConstraint())
            .setWidth(new PixelConstraint(parent.getWidth()))
            .setHeight(new PixelConstraint(parent.getHeight()))
            .setChildOf(parent);

        scrollComponent = new ScrollComponent(
            "No options available",
            0f
        )
            .setX(new CenterConstraint())
            .setY(new PixelConstraint(20f * scalingFactor))
            .setWidth(new PixelConstraint(parent.getWidth() - 20f * scalingFactor))
            .setHeight(new PixelConstraint(parent.getHeight() - 50f * scalingFactor))
            .setChildOf(this);

        initOptions();

        generateOptionsUI(parent);
    }

    /**
     * Initialize the options for this category.
     * This method should be overridden by subclasses to add their specific options.
     */
    protected abstract void initOptions();

    /**
     * Add an option to this category.
     * @param option The option to add
     */
    protected void addOption(ConfigOption option) {
        options.add(option);
    }

    /**
     * Generate UI elements for all options.
     */
    private void generateOptionsUI(UIComponent parent) {
        for (ConfigOption option : options) {
            UIComponent optionContainer = new UIContainer()
                .setX(new CenterConstraint())
                .setY(new SiblingConstraint(20f * scalingFactor))
                .setWidth(new PixelConstraint(parent.getWidth() - 40f * scalingFactor))
                .setHeight(new PixelConstraint(100f * scalingFactor))
                .setChildOf(scrollComponent);

            UIComponent descriptionContainer = new UIContainer()
                .setX(new PixelConstraint(10f * scalingFactor))
                .setY(new PixelConstraint(10f * scalingFactor))
                .setWidth(new PixelConstraint(500f * scalingFactor))
                .setHeight(new PixelConstraint(100f * scalingFactor))
                .setChildOf(optionContainer);

            UIComponent title = new UIText(option.getDisplayName())
                .setX(new PixelConstraint(0f))
                .setY(new PixelConstraint(0f))
                .setWidth(new ScaledTextConstraint(1.8f * scalingFactor))
                    .setHeight(new ScaledTextConstraint(1.8f * scalingFactor))
                .setChildOf(descriptionContainer);

            new MultiLineTextComponent(
                option.getDescription(),
                500f * scalingFactor,
                1.5f * scalingFactor,
                new Color(200, 200, 200),
                    descriptionContainer,
                    first
            )
                .setX(new PixelConstraint(10f * scalingFactor))
                .setY(new PixelConstraint(30f * scalingFactor));
            if (first) {appear(title, 0.3f);}
            UIComponent inputContainer = new UIContainer()
                .setX(new SiblingConstraint(20f * scalingFactor))
                .setY(new PixelConstraint(0f))
                .setWidth(new PixelConstraint(optionContainer.getWidth() - descriptionContainer.getWidth()))
                .setHeight(new PixelConstraint(100f * scalingFactor))
                .setChildOf(optionContainer);

            switch (option.getType()) {
                case ConfigOption.TYPE_BOOLEAN:
                    addBooleanInput(inputContainer, option);
                    break;
                case ConfigOption.TYPE_NUMERIC:
                    addNumericInput(inputContainer, option);
                    break;
                case ConfigOption.TYPE_STRING:
                    addStringInput(inputContainer, option);
                    break;
            }
        }
    }

    /**
     * Add a boolean input (toggle) for a boolean option.
     * @param container The parent container
     * @param option The option to create an input for
     */
    private void addBooleanInput(UIComponent container, ConfigOption option) {
        boolean currentValue = FeatureConfig.isEnabled(option.getId());
        ToggleSwitchComponent switchComponent = new ToggleSwitchComponent(container, currentValue, 30f * scalingFactor, 60f * scalingFactor, first, option.getId());
    }

    /**
     * Add a numeric input (slider) for a numeric option.
     * @param container The parent container
     * @param option The option to create an input for
     */
    private void addNumericInput(UIComponent container, ConfigOption option) {
        Double currentValue = ConfigManager.getSetting(option.getId());
        if (currentValue == null) {
            currentValue = option.getDefaultNumericValue();
        }

        // Create slider with min/max values from the option
        SliderComponent slider = new SliderComponent(
            currentValue,
            option.getMinValue(),
            option.getMaxValue(),
            container,
            first,
            30f * scalingFactor,
            200f * scalingFactor
        );

        // Update config when value changes
        slider.setOnValueChanged(value -> {
            ConfigManager.setSetting(option.getId(), value);
        });
    }

    /**
     * Add a string input (text field) for a string option.
     * @param container The parent container
     * @param option The option to create an input for
     */
    private void addStringInput(UIComponent container, ConfigOption option) {
    }
}
