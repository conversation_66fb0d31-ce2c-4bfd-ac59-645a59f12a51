package com.ifallious.utils.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.ifallious.Wynnutils;
import com.ifallious.utils.ErrorReporter;
import gg.essential.universal.UChat;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Field;

import org.apache.commons.lang3.exception.ExceptionUtils;

public class ConfigManager {
    private static final String CONFIG_FILE_NAME = "config.json";
    private static final File CONFIG_FILE = new File(Wynnutils.FOLDER, CONFIG_FILE_NAME);
    private static Config config = null;

    /**
     * Load the configuration from file
     * @return The loaded configuration
     */
    public static Config loadConfig() {
        try {
            if (CONFIG_FILE.exists()) {
                try (FileReader reader = new FileReader(CONFIG_FILE)) {
                    Gson gson = new Gson();
                    config = gson.fromJson(reader, Config.class);
                    updateConfigWithDefaults();
                }
            } else {
                config = new Config();
                saveConfig();
            }
        } catch (IOException e) {
            Wynnutils.LOGGER.error("Error loading configuration: " + e.getMessage(), e);
            ErrorReporter.reportError("Error loading configuration", e.getMessage() + ExceptionUtils.getStackTrace(e));
            config = new Config();
        }

        return config;
    }

    /**
     * Update the configuration with default values for any missing fields
     */
    private static void updateConfigWithDefaults() {
        if (config == null) return;

        if (config.getFeatures() == null) {
            config.setFeatures(new ConfigFeatures());
        } else {
            ConfigFeatures defaultFeatures = new ConfigFeatures();
            updateMissingFields(config.getFeatures(), defaultFeatures);
        }

        if (config.getSettings() == null) {
            config.setSettings(new ConfigSettings());
        } else {
            ConfigSettings defaultSettings = new ConfigSettings();
            updateMissingFields(config.getSettings(), defaultSettings);
        }

        config.setVersion(Wynnutils.VERSION);

        if (config.getSelectedCycle() == null) {
            config.setSelectedCycle("");
        }
    }

    /**
     * Helper method to update missing fields with default values
     * @param target The target object to update
     * @param defaults The default object to get values from
     */
    private static void updateMissingFields(Object target, Object defaults) {
        try {
            for (Field field : defaults.getClass().getDeclaredFields()) {
                field.setAccessible(true);
                Object defaultValue = field.get(defaults);

                if (field.get(target) == null) {
                    field.set(target, defaultValue);
                }
            }
        } catch (IllegalAccessException e) {
            Wynnutils.LOGGER.error("Error updating configuration fields: " + e.getMessage(), e);
            ErrorReporter.reportError("Error updating configuration fields", e.getMessage() + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * Save the configuration to file
     * @return True if saved successfully, false otherwise
     */
    public static boolean saveConfig() {
        try {
            if (config != null) {
                if (!CONFIG_FILE.getParentFile().exists()) {
                    CONFIG_FILE.getParentFile().mkdirs();
                }

                try (FileWriter writer = new FileWriter(CONFIG_FILE)) {
                    Gson gson = new GsonBuilder().setPrettyPrinting().create();
                    gson.toJson(config, writer);
                }
                return true;
            }
            return false;
        } catch (IOException e) {
            Wynnutils.LOGGER.error("Error saving configuration: " + e.getMessage(), e);
            ErrorReporter.reportError("Error saving configuration", e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * Get a feature value by name
     * @param featureName The name of the feature
     * @return The feature value, or null if not found
     */
    public static Boolean getFeature(String featureName) {
        if (config == null) {
            loadConfig();
        }

        if (config.getFeatures() == null) {
            return null;
        }

        try {
            Field field = ConfigFeatures.class.getField(featureName);
            return (Boolean) field.get(config.getFeatures());
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Error getting feature " + featureName + ": " + e.getMessage(), e);
            ErrorReporter.reportError("Error getting feature " + featureName, e.getMessage() + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     * Set a feature value by name
     * @param featureName The name of the feature
     * @param value The value to set
     * @return True if set successfully, false otherwise
     */
    public static boolean setFeature(String featureName, boolean value) {
        if (config == null) {
            loadConfig();
        }

        if (config.getFeatures() == null) {
            config.setFeatures(new ConfigFeatures());
        }

        try {
            Field field = ConfigFeatures.class.getField(featureName);
            field.set(config.getFeatures(), value);
            return saveConfig();
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Error setting feature " + featureName + ": " + e.getMessage(), e);
            ErrorReporter.reportError("Error setting feature " + featureName, e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * Get the selected cycle
     * @return The selected cycle name
     */
    public static String getSelectedCycle() {
        if (config == null) {
            loadConfig();
        }

        return config.getSelectedCycle();
    }

    /**
     * Set the selected cycle
     * @param cycleName The cycle name to set
     * @return True if set successfully, false otherwise
     */
    public static boolean setSelectedCycle(String cycleName) {
        if (config == null) {
            loadConfig();
        }

        config.setSelectedCycle(cycleName != null ? cycleName : "");
        return saveConfig();
    }

    /**
     * Get a setting value by name
     * @param settingName The name of the setting
     * @return The setting value, or null if not found
     */
    public static Double getSetting(String settingName) {
        if (config == null) {
            loadConfig();
        }

        if (config.getSettings() == null) {
            return null;
        }

        try {
            Field field = ConfigSettings.class.getField(settingName);
            return (Double) field.get(config.getSettings());
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Error getting setting " + settingName + ": " + e.getMessage(), e);
            ErrorReporter.reportError("Error getting setting " + settingName, e.getMessage() + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     * Set a setting value by name
     * @param settingName The name of the setting
     * @param value The value to set
     * @return True if set successfully, false otherwise
     */
    public static boolean setSetting(String settingName, double value) {
        if (config == null) {
            loadConfig();
        }

        if (config.getSettings() == null) {
            config.setSettings(new ConfigSettings());
        }

        try {
            Field field = ConfigSettings.class.getField(settingName);
            field.set(config.getSettings(), value);
            return saveConfig();
        } catch (Exception e) {
            Wynnutils.LOGGER.error("Error setting setting " + settingName + ": " + e.getMessage(), e);
            ErrorReporter.reportError("Error setting setting " + settingName, e.getMessage() + ExceptionUtils.getStackTrace(e));
            return false;
        }
    }

    /**
     * Get the current configuration
     * @return The current configuration
     */
    public static Config getConfig() {
        if (config == null) {
            loadConfig();
        }

        return config;
    }
}
