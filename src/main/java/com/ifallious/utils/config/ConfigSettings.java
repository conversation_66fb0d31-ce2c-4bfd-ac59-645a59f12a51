package com.ifallious.utils.config;

/**
 * Configuration class for numerical settings in the mod
 *
 * All fields are public for direct access, eliminating the need for getters and setters.
 * The ConfigManager uses reflection to access these fields by name.
 */
public class ConfigSettings {
    // Velocity multiplier for the StrongerCharge feature
    public double chargeVelocityMultiplier = 1.2;
    public double autoTransferThreshold = 150;

    public ConfigSettings() {
    }
}
