package com.ifallious.utils.ai.model;

import java.util.List;

/**
 * OpenAI-compatible chat message model.
 * Supports assistant tool_calls and tool role messages.
 */
public class AIMessage {
    public String role;
    public String content;

    // For tool role messages
    public String name; // optional

    public AIMessage() {}
    public AIMessage(String role, String content) {
        this.role = role;
        this.content = content;
    }

    public static AIMessage system(String content) { return new AIMessage("system", content); }
    public static AIMessage user(String content) { return new AIMessage("user", content); }
    public static AIMessage assistant(String content) { return new AIMessage("assistant", content); }
}

